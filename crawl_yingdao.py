import requests
import json
import os

FIRECRAWL_URL = "http://localhost:3002"
DOC_URL = "https://www.yingdao.com/yddoc/rpa"
OUTPUT_DIR = "./yingdao_rpa_docs"

def crawl_and_save():
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)

    print(f"Starting crawl for {DOC_URL}")
    try:
        response = requests.post(
            f"{FIRECRAWL_URL}/v1/crawl",
            headers={"Content-Type": "application/json"},
            json={
                "url": DOC_URL
            }
        )
        response.raise_for_status()
        job_info = response.json()
        job_id = job_info["id"]
        print(f"Crawl job started with ID: {job_id}")

        # Poll for job completion
        while True:
            status_response = requests.get(f"{FIRECRAWL_URL}/v1/crawl/{job_id}")
            status_response.raise_for_status()
            status_data = status_response.json()
            if status_data["status"] == "completed":
                print("Crawl job completed.")
                break
            elif status_data["status"] == "failed":
                print("Crawl job failed: {}".format(status_data.get("error", "Unknown error")))
                return
            else:
                print("Crawl job status: {}. Waiting...".format(status_data["status"]))
                import time
                time.sleep(5)

        # Retrieve and save results
        for item in status_data["data"]:
            url = item["url"]
            markdown_content = item["markdown"]
            title = item.get("title", "no_title").replace("/", "_").replace(" ", "_")
            
            # Create a more descriptive filename
            # Use a simpler approach for filename to avoid complex f-string escaping issues
            cleaned_url = url.replace("https://", "").replace("http://", "").replace("/", "_").replace(".", "_")
            filename = f"{title}_{cleaned_url}.md"
            filepath = os.path.join(OUTPUT_DIR, filename)
            
            with open(filepath, "w", encoding="utf-8") as f:
                f.write(markdown_content)
            print(f"Saved: {filepath}")

    except requests.exceptions.RequestException as e:
        print(f"Error during API call: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    crawl_and_save()


